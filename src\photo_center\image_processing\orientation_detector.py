"""
Automatic orientation detection for human subjects.

This module analyzes human pose keypoints to detect when subjects are
upside down or sideways and determines the correct rotation needed.
"""

import math
import numpy as np
import cv2
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass

from ..utils.config import Config
from ..utils.logger import get_logger


@dataclass
class OrientationResult:
    """Result of orientation detection analysis."""
    needs_rotation: bool
    rotation_angle: int  # 0, 90, 180, or 270 degrees
    confidence: float  # 0.0 to 1.0
    method_used: str
    analysis_details: Dict[str, Any]


class OrientationDetector:
    """Detects and corrects human subject orientation in images."""
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize orientation detector.
        
        Args:
            config: Configuration object. If None, creates default config.
        """
        self.config = config or Config()
        self.logger = get_logger(__name__)
        
        # Load configuration settings
        self.enabled = self.config.get('orientation_detection.enabled', True)
        self.method = self.config.get('orientation_detection.method', 'head_body_vector')
        self.confidence_threshold = self.config.get('orientation_detection.confidence_threshold', 0.7)
        self.head_body_angle_threshold = self.config.get('orientation_detection.head_body_angle_threshold', 45)
        self.shoulder_angle_threshold = self.config.get('orientation_detection.shoulder_angle_threshold', 30)
        self.min_keypoints_required = self.config.get('orientation_detection.min_keypoints_required', 4)
        self.rotation_angles = self.config.get('orientation_detection.rotation_angles', [90, 180, 270])
        self.prefer_minimal_rotation = self.config.get('orientation_detection.prefer_minimal_rotation', True)
        
        self.logger.debug(f"OrientationDetector initialized: enabled={self.enabled}, method={self.method}")
    
    def detect_orientation(
        self,
        image: np.ndarray,
        detection: Dict[str, Any]
    ) -> OrientationResult:
        """Detect if the subject needs rotation and determine the angle.
        
        Args:
            image: Input image as numpy array
            detection: Detection dictionary with keypoints
            
        Returns:
            OrientationResult with rotation recommendation
        """
        if not self.enabled:
            return OrientationResult(
                needs_rotation=False,
                rotation_angle=0,
                confidence=1.0,
                method_used='disabled',
                analysis_details={'reason': 'orientation detection disabled'}
            )
        
        keypoints = detection.get('keypoints', {})
        if not keypoints or len(keypoints) < self.min_keypoints_required:
            return OrientationResult(
                needs_rotation=False,
                rotation_angle=0,
                confidence=0.0,
                method_used='insufficient_keypoints',
                analysis_details={'keypoint_count': len(keypoints), 'required': self.min_keypoints_required}
            )
        
        # Apply the selected detection method
        if self.method == 'head_body_vector':
            return self._detect_by_head_body_vector(keypoints)
        elif self.method == 'shoulder_alignment':
            return self._detect_by_shoulder_alignment(keypoints)
        elif self.method == 'combined':
            return self._detect_by_combined_analysis(keypoints)
        else:
            self.logger.warning(f"Unknown orientation detection method: {self.method}")
            return OrientationResult(
                needs_rotation=False,
                rotation_angle=0,
                confidence=0.0,
                method_used='unknown_method',
                analysis_details={'method': self.method}
            )
    
    def _detect_by_head_body_vector(self, keypoints: Dict[str, Tuple[int, int]]) -> OrientationResult:
        """Detect orientation by analyzing head-to-body vector.
        
        Args:
            keypoints: Dictionary of keypoint coordinates
            
        Returns:
            OrientationResult
        """
        # Get head center (nose, eyes)
        head_points = []
        for kp in ['nose', 'left_eye', 'right_eye']:
            if kp in keypoints:
                head_points.append(keypoints[kp])
        
        # Get body center (shoulders, hips)
        body_points = []
        for kp in ['left_shoulder', 'right_shoulder', 'left_hip', 'right_hip']:
            if kp in keypoints:
                body_points.append(keypoints[kp])
        
        if len(head_points) < 1 or len(body_points) < 1:
            return OrientationResult(
                needs_rotation=False,
                rotation_angle=0,
                confidence=0.0,
                method_used='head_body_vector',
                analysis_details={'head_points': len(head_points), 'body_points': len(body_points)}
            )
        
        # Calculate centers
        head_center = np.mean(head_points, axis=0)
        body_center = np.mean(body_points, axis=0)
        
        # Calculate vector from body to head (should point upward in correct orientation)
        vector = head_center - body_center
        
        # Calculate angle from vertical (0 degrees = straight up)
        angle_rad = math.atan2(vector[0], -vector[1])  # -y because image coordinates are flipped
        angle_deg = math.degrees(angle_rad)
        
        # Normalize angle to 0-360 range
        if angle_deg < 0:
            angle_deg += 360
        
        # Determine rotation needed
        rotation_angle, confidence = self._calculate_rotation_from_angle(angle_deg)
        
        analysis_details = {
            'head_center': head_center.tolist(),
            'body_center': body_center.tolist(),
            'vector': vector.tolist(),
            'angle_degrees': angle_deg,
            'head_points_count': len(head_points),
            'body_points_count': len(body_points)
        }
        
        return OrientationResult(
            needs_rotation=rotation_angle != 0,
            rotation_angle=rotation_angle,
            confidence=confidence,
            method_used='head_body_vector',
            analysis_details=analysis_details
        )
    
    def _detect_by_shoulder_alignment(self, keypoints: Dict[str, Tuple[int, int]]) -> OrientationResult:
        """Detect orientation by analyzing shoulder alignment.
        
        Args:
            keypoints: Dictionary of keypoint coordinates
            
        Returns:
            OrientationResult
        """
        if 'left_shoulder' not in keypoints or 'right_shoulder' not in keypoints:
            return OrientationResult(
                needs_rotation=False,
                rotation_angle=0,
                confidence=0.0,
                method_used='shoulder_alignment',
                analysis_details={'reason': 'missing shoulder keypoints'}
            )
        
        left_shoulder = np.array(keypoints['left_shoulder'])
        right_shoulder = np.array(keypoints['right_shoulder'])
        
        # Calculate shoulder line vector
        shoulder_vector = right_shoulder - left_shoulder
        
        # Calculate angle from horizontal (0 degrees = horizontal)
        angle_rad = math.atan2(shoulder_vector[1], shoulder_vector[0])
        angle_deg = math.degrees(angle_rad)
        
        # Normalize to 0-360 range
        if angle_deg < 0:
            angle_deg += 360
        
        # For shoulders, we expect them to be roughly horizontal (0° or 180°)
        # Calculate deviation from horizontal
        horizontal_deviation = min(abs(angle_deg), abs(angle_deg - 180), abs(angle_deg - 360))
        
        # If shoulders are more vertical than horizontal, rotation is needed
        if horizontal_deviation > self.shoulder_angle_threshold:
            # Determine rotation based on shoulder angle
            if 45 <= angle_deg <= 135:  # Shoulders pointing down-right to down-left
                rotation_angle = 270  # Rotate counter-clockwise
            elif 225 <= angle_deg <= 315:  # Shoulders pointing up-left to up-right  
                rotation_angle = 90   # Rotate clockwise
            else:
                rotation_angle = 180  # Upside down
            
            confidence = min(1.0, (horizontal_deviation - self.shoulder_angle_threshold) / 45.0)
        else:
            rotation_angle = 0
            confidence = 1.0 - (horizontal_deviation / self.shoulder_angle_threshold)
        
        analysis_details = {
            'left_shoulder': left_shoulder.tolist(),
            'right_shoulder': right_shoulder.tolist(),
            'shoulder_vector': shoulder_vector.tolist(),
            'angle_degrees': angle_deg,
            'horizontal_deviation': horizontal_deviation
        }
        
        return OrientationResult(
            needs_rotation=rotation_angle != 0,
            rotation_angle=rotation_angle,
            confidence=confidence,
            method_used='shoulder_alignment',
            analysis_details=analysis_details
        )
    
    def _detect_by_combined_analysis(self, keypoints: Dict[str, Tuple[int, int]]) -> OrientationResult:
        """Detect orientation using combined head-body vector and shoulder alignment.
        
        Args:
            keypoints: Dictionary of keypoint coordinates
            
        Returns:
            OrientationResult with highest confidence method
        """
        head_body_result = self._detect_by_head_body_vector(keypoints)
        shoulder_result = self._detect_by_shoulder_alignment(keypoints)
        
        # Choose result with higher confidence
        if head_body_result.confidence >= shoulder_result.confidence:
            result = head_body_result
            result.method_used = 'combined_head_body'
            result.analysis_details['shoulder_analysis'] = shoulder_result.analysis_details
        else:
            result = shoulder_result
            result.method_used = 'combined_shoulder'
            result.analysis_details['head_body_analysis'] = head_body_result.analysis_details
        
        return result
    
    def _calculate_rotation_from_angle(self, angle_deg: float) -> Tuple[int, float]:
        """Calculate rotation angle and confidence from head-body vector angle.
        
        Args:
            angle_deg: Angle in degrees (0-360)
            
        Returns:
            Tuple of (rotation_angle, confidence)
        """
        # Define expected angle ranges for each orientation
        # 0° = head directly above body (correct)
        # 90° = head to the right of body (needs 270° rotation)
        # 180° = head below body (needs 180° rotation)  
        # 270° = head to the left of body (needs 90° rotation)
        
        angle_ranges = [
            (0, 0, 45),      # Correct orientation: -45° to +45°
            (90, 45, 135),   # 90° clockwise: 45° to 135°
            (180, 135, 225), # Upside down: 135° to 225°
            (270, 225, 315), # 90° counter-clockwise: 225° to 315°
            (0, 315, 360)    # Correct orientation: 315° to 360° (same as 0°)
        ]
        
        for expected_angle, min_range, max_range in angle_ranges:
            if min_range <= angle_deg < max_range:
                if expected_angle == 0:
                    rotation_needed = 0
                elif expected_angle == 90:
                    rotation_needed = 270  # Rotate counter-clockwise to fix
                elif expected_angle == 180:
                    rotation_needed = 180
                elif expected_angle == 270:
                    rotation_needed = 90   # Rotate clockwise to fix
                
                # Calculate confidence based on how close to expected angle
                center_angle = (min_range + max_range) / 2
                if center_angle == 0 and angle_deg > 315:  # Handle wrap-around
                    deviation = min(angle_deg - 315, 360 - angle_deg + 45)
                else:
                    deviation = abs(angle_deg - center_angle)
                
                max_deviation = (max_range - min_range) / 2
                confidence = 1.0 - (deviation / max_deviation)
                confidence = max(0.0, min(1.0, confidence))
                
                return rotation_needed, confidence
        
        # Fallback (shouldn't reach here)
        return 0, 0.0
    
    def apply_rotation(self, image: np.ndarray, rotation_angle: int) -> np.ndarray:
        """Apply rotation to image.
        
        Args:
            image: Input image
            rotation_angle: Rotation angle (90, 180, or 270 degrees)
            
        Returns:
            Rotated image
        """
        if rotation_angle == 0:
            return image
        elif rotation_angle == 90:
            return cv2.rotate(image, cv2.ROTATE_90_CLOCKWISE)
        elif rotation_angle == 180:
            return cv2.rotate(image, cv2.ROTATE_180)
        elif rotation_angle == 270:
            return cv2.rotate(image, cv2.ROTATE_90_COUNTERCLOCKWISE)
        else:
            self.logger.warning(f"Unsupported rotation angle: {rotation_angle}")
            return image
