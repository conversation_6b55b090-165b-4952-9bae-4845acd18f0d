"""Enhanced centering with aggressive cropping for perfect subject positioning."""

import cv2
import numpy as np
from typing import Dict, Any, Tuple, Optional, List
from dataclasses import dataclass

from ..utils.logger import get_logger
from ..utils.config import Config
from .centering import CenteringResult


@dataclass
class CropCenteringResult:
    """Result of crop-based centering operation."""
    cropped_image: np.ndarray
    crop_box: Tuple[int, int, int, int]  # (x1, y1, x2, y2)
    subject_center: Tuple[int, int]
    target_center: Tuple[int, int]
    confidence: float
    method_used: str
    crop_ratio: float  # Ratio of cropped area to original area
    padding_needed: Tuple[int, int]  # (left/right, top/bottom) padding needed for original size


class CropCenterer:
    """Enhanced photo centerer that prioritizes perfect centering over preserving entire image."""
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize crop centerer.
        
        Args:
            config: Configuration object. If None, creates default config.
        """
        self.config = config or Config()
        self.logger = get_logger(__name__)
    
    def center_subject_with_crop(
        self,
        image: np.ndarray,
        detection: Dict[str, Any],
        target_size: Optional[Tuple[int, int]] = None,
        centering_method: str = 'face_chest_based'
    ) -> CropCenteringResult:
        """Center subject with aggressive cropping for perfect positioning.

        Args:
            image: Input image as numpy array (8-bit or 16-bit)
            detection: Detection dictionary from human detector
            target_size: Target output size (width, height). If None, uses original size
            centering_method: Method to use for finding subject center

        Returns:
            CropCenteringResult with perfectly centered subject (preserves original bit depth)
        """
        # First, determine the subject center using the specified method
        subject_center = self._calculate_subject_center(image, detection, centering_method)

        # Perform aggressive cropping for perfect centering
        return self._perform_crop_centering(
            image, subject_center, detection, target_size, centering_method
        )

    def center_two_people_with_crop(
        self,
        image: np.ndarray,
        detections: List[Dict[str, Any]],
        target_size: Optional[Tuple[int, int]] = None,
        centering_method: str = 'face_chest_based'
    ) -> CropCenteringResult:
        """Center two people with aggressive cropping for perfect positioning.

        Args:
            image: Input image as numpy array (8-bit or 16-bit)
            detections: List of detection dictionaries (should contain at least 2 people)
            target_size: Target output size (width, height). If None, uses original size
            centering_method: Method to use for finding subject centers

        Returns:
            CropCenteringResult with perfectly centered two people (preserves original bit depth)
        """
        if len(detections) < 2:
            self.logger.warning("Two-person crop centering requested but less than 2 people detected, using single person centering")
            return self.center_subject_with_crop(image, detections[0] if detections else {}, target_size, centering_method)

        # Get the two most centered people
        image_height, image_width = image.shape[:2]
        image_center_x = image_width / 2
        image_center_y = image_height / 2

        # Calculate distance from image center for each detection
        def distance_from_center(detection):
            center = detection.get('center', (0, 0))
            return np.sqrt((center[0] - image_center_x)**2 + (center[1] - image_center_y)**2)

        # Sort detections by distance from center and take the two closest
        sorted_detections = sorted(detections, key=distance_from_center)
        person1, person2 = sorted_detections[0], sorted_detections[1]

        self.logger.debug(f"Two-person crop centering: selected people at {person1['center']} and {person2['center']}")

        # Calculate centers for both people
        center1 = self._calculate_subject_center(image, person1, centering_method)
        center2 = self._calculate_subject_center(image, person2, centering_method)

        # Calculate midpoint
        midpoint_x = int((center1[0] + center2[0]) / 2)
        midpoint_y = int((center1[1] + center2[1]) / 2)
        midpoint = (midpoint_x, midpoint_y)

        self.logger.debug(f"Two-person midpoint: {midpoint} (from {center1} and {center2})")

        # Create a combined detection for centering purposes
        combined_detection = {
            'bbox': self._get_combined_bbox([person1, person2]),
            'confidence': min(person1['confidence'], person2['confidence']),
            'center': midpoint,
            'keypoints': {}  # We'll use the midpoint directly
        }

        return self._perform_crop_centering(
            image, midpoint, combined_detection, target_size, f'two_people_{centering_method}'
        )
    
    def _calculate_subject_center(
        self,
        image: np.ndarray,
        detection: Dict[str, Any],
        method: str
    ) -> Tuple[int, int]:
        """Calculate subject center using specified method.
        
        Args:
            image: Input image
            detection: Detection data
            method: Centering method to use
            
        Returns:
            Subject center coordinates (x, y)
        """
        keypoints = detection.get('keypoints', {})
        
        if method == 'face_chest_based' and keypoints:
            return self._get_face_chest_center(keypoints)
        elif method == 'keypoint_based' and keypoints:
            return self._get_keypoint_center(keypoints)
        elif method == 'bbox_based':
            return self._get_bbox_center(detection)
        elif method == 'center_of_mass':
            return self._get_mass_center(image, detection)
        else:
            # Fallback to bbox center
            return self._get_bbox_center(detection)
    
    def _get_face_chest_center(self, keypoints: Dict[str, Tuple[int, int]]) -> Tuple[int, int]:
        """Calculate weighted center of face, chest, and hip keypoints."""
        face_weight = self.config.face_weight
        chest_weight = self.config.chest_weight
        hip_weight = self.config.hip_weight
        
        # Face keypoints (higher weight)
        face_points = []
        for name in ['nose', 'left_eye', 'right_eye']:
            if name in keypoints:
                face_points.append(keypoints[name])
        
        # Chest/shoulder keypoints
        chest_points = []
        for name in ['left_shoulder', 'right_shoulder']:
            if name in keypoints:
                chest_points.append(keypoints[name])
        
        # Hip keypoints
        hip_points = []
        for name in ['left_hip', 'right_hip']:
            if name in keypoints:
                hip_points.append(keypoints[name])
        
        # Build weighted points list
        weighted_points = []
        weights = []
        
        # Add face points
        for point in face_points:
            weighted_points.append(point)
            weights.append(face_weight / len(face_points) if face_points else 0)
        
        # Add chest points
        for point in chest_points:
            weighted_points.append(point)
            weights.append(chest_weight / len(chest_points) if chest_points else 0)
        
        # Add hip points
        for point in hip_points:
            weighted_points.append(point)
            weights.append(hip_weight / len(hip_points) if hip_points else 0)
        
        if not weighted_points:
            # Fallback to all available keypoints
            return self._get_keypoint_center(keypoints)
        
        # Calculate weighted center
        weighted_points = np.array(weighted_points)
        weights = np.array(weights)
        subject_center = np.average(weighted_points, axis=0, weights=weights)
        
        return tuple(map(int, subject_center))
    
    def _get_keypoint_center(self, keypoints: Dict[str, Tuple[int, int]]) -> Tuple[int, int]:
        """Calculate center from all available keypoints."""
        if not keypoints:
            raise ValueError("No keypoints available for centering")
        
        points = list(keypoints.values())
        center = np.mean(points, axis=0)
        return tuple(map(int, center))
    
    def _get_bbox_center(self, detection: Dict[str, Any]) -> Tuple[int, int]:
        """Calculate center from bounding box."""
        bbox = detection['bbox']
        x1, y1, x2, y2 = bbox
        return ((x1 + x2) // 2, (y1 + y2) // 2)
    
    def _get_mass_center(self, image: np.ndarray, detection: Dict[str, Any]) -> Tuple[int, int]:
        """Calculate center of mass within bounding box."""
        bbox = detection['bbox']
        x1, y1, x2, y2 = bbox
        
        # Extract region of interest
        roi = image[y1:y2, x1:x2]
        gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        
        # Calculate center of mass
        moments = cv2.moments(gray_roi)
        if moments['m00'] != 0:
            cx = int(moments['m10'] / moments['m00']) + x1
            cy = int(moments['m01'] / moments['m00']) + y1
            return (cx, cy)
        else:
            # Fallback to bbox center
            return self._get_bbox_center(detection)

    def _get_combined_bbox(self, detections: List[Dict[str, Any]]) -> List[int]:
        """Get combined bounding box that encompasses all detections."""
        if not detections:
            return [0, 0, 0, 0]

        min_x1 = min(det['bbox'][0] for det in detections)
        min_y1 = min(det['bbox'][1] for det in detections)
        max_x2 = max(det['bbox'][2] for det in detections)
        max_y2 = max(det['bbox'][3] for det in detections)

        return [min_x1, min_y1, max_x2, max_y2]

    def _perform_crop_centering(
        self,
        image: np.ndarray,
        subject_center: Tuple[int, int],
        detection: Dict[str, Any],
        target_size: Optional[Tuple[int, int]],
        method: str
    ) -> CropCenteringResult:
        """Perform aggressive cropping for perfect centering.

        Args:
            image: Input image
            subject_center: Center point of the subject
            detection: Original detection data
            target_size: Target output size
            method: Method used for centering

        Returns:
            CropCenteringResult with perfectly centered subject
        """
        height, width = image.shape[:2]
        subject_x, subject_y = subject_center

        # Get target position from config
        target_pos = self.config.target_position

        self.logger.debug(f"Crop centering: image={width}x{height}, subject=({subject_x},{subject_y}), target_pos={target_pos}")

        # Determine output size
        if target_size is None:
            output_width, output_height = width, height
        else:
            output_width, output_height = target_size

        # Calculate target center in output image
        target_center_x = int(output_width * target_pos[0])
        target_center_y = int(output_height * target_pos[1])

        self.logger.debug(f"Target center in output: ({target_center_x},{target_center_y}) for {output_width}x{output_height}")

        # Calculate ideal crop box for perfect centering (may go outside image bounds)
        crop_x1 = subject_x - target_center_x
        crop_y1 = subject_y - target_center_y
        crop_x2 = crop_x1 + output_width
        crop_y2 = crop_y1 + output_height

        self.logger.debug(f"Ideal crop box: ({crop_x1},{crop_y1}) to ({crop_x2},{crop_y2})")

        # Calculate how much we can actually crop while keeping subject centered
        # Priority: perfect left-right centering, then best possible up-down centering

        # For horizontal centering, calculate maximum possible crop width
        max_left_crop = subject_x  # Distance from subject to left edge
        max_right_crop = width - subject_x  # Distance from subject to right edge
        max_crop_width = 2 * min(max_left_crop, max_right_crop)

        # For vertical centering, calculate maximum possible crop height
        max_top_crop = subject_y  # Distance from subject to top edge
        max_bottom_crop = height - subject_y  # Distance from subject to bottom edge
        max_crop_height = 2 * min(max_top_crop, max_bottom_crop)

        self.logger.debug(f"Max crop dimensions: {max_crop_width}x{max_crop_height}")

        # Determine actual crop dimensions
        if output_width <= max_crop_width and output_height <= max_crop_height:
            # Perfect centering is possible
            actual_width = output_width
            actual_height = output_height
            perfect_centering = True
        else:
            # Need to compromise - prioritize horizontal centering
            if output_width <= max_crop_width:
                # Can achieve perfect horizontal centering
                actual_width = output_width
                actual_height = min(output_height, max_crop_height)
            else:
                # Must compromise on both dimensions
                actual_width = max_crop_width
                actual_height = max_crop_height
            perfect_centering = False

        # Calculate final crop box with subject perfectly centered
        final_crop_x1 = subject_x - actual_width // 2
        final_crop_y1 = subject_y - actual_height // 2
        final_crop_x2 = final_crop_x1 + actual_width
        final_crop_y2 = final_crop_y1 + actual_height

        # Ensure crop box is within image bounds
        final_crop_x1 = max(0, final_crop_x1)
        final_crop_y1 = max(0, final_crop_y1)
        final_crop_x2 = min(width, final_crop_x2)
        final_crop_y2 = min(height, final_crop_y2)

        self.logger.debug(f"Final crop box: ({final_crop_x1},{final_crop_y1}) to ({final_crop_x2},{final_crop_y2})")

        # Crop the image
        cropped_image = image[final_crop_y1:final_crop_y2, final_crop_x1:final_crop_x2]

        # Calculate crop ratio
        original_area = width * height
        cropped_area = (final_crop_x2 - final_crop_x1) * (final_crop_y2 - final_crop_y1)
        crop_ratio = cropped_area / original_area

        # Calculate padding needed to restore original size
        cropped_width = final_crop_x2 - final_crop_x1
        cropped_height = final_crop_y2 - final_crop_y1

        horizontal_padding = max(0, width - cropped_width)
        vertical_padding = max(0, height - cropped_height)
        padding_needed = (horizontal_padding, vertical_padding)

        # Resize if target size specified and different from crop size
        if target_size and (cropped_image.shape[1] != output_width or cropped_image.shape[0] != output_height):
            cropped_image = cv2.resize(cropped_image, (output_width, output_height), interpolation=cv2.INTER_LANCZOS4)

        # Calculate final target center in cropped image
        final_target_center = (target_center_x, target_center_y)

        # Calculate confidence based on centering quality
        actual_subject_x = subject_x - final_crop_x1
        actual_subject_y = subject_y - final_crop_y1

        if target_size:
            # Scale coordinates if image was resized
            scale_x = output_width / cropped_width
            scale_y = output_height / cropped_height
            actual_subject_x = int(actual_subject_x * scale_x)
            actual_subject_y = int(actual_subject_y * scale_y)

        # Calculate distance from target
        distance = np.sqrt((actual_subject_x - target_center_x)**2 + (actual_subject_y - target_center_y)**2)
        max_distance = np.sqrt(output_width**2 + output_height**2)
        confidence = max(0.0, 1.0 - (distance / max_distance))

        # Boost confidence if we achieved perfect horizontal centering
        if perfect_centering:
            confidence = min(1.0, confidence + 0.1)

        self.logger.debug(f"Crop centering completed: method={method}, confidence={confidence:.3f}, crop_ratio={crop_ratio:.3f}")

        return CropCenteringResult(
            cropped_image=cropped_image,
            crop_box=(final_crop_x1, final_crop_y1, final_crop_x2, final_crop_y2),
            subject_center=subject_center,
            target_center=final_target_center,
            confidence=confidence,
            method_used=f"{method}_crop",
            crop_ratio=crop_ratio,
            padding_needed=padding_needed
        )
